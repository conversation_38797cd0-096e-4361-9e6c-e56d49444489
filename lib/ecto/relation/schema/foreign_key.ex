defmodule Ecto.Relation.Schema.ForeignKey do
  @moduledoc """
  Represents a foreign key relationship in a database table/schema.

  This struct stores information about a foreign key field and its reference
  to another table and field.

  ## Examples

      # Simple foreign key
      %Ecto.Relation.Schema.ForeignKey{
        field: :user_id,
        references_table: "users",
        references_field: :id,
        association_name: :user
      }
  """

  @derive Jason.Encoder
  @type t :: %__MODULE__{
          field: atom(),
          references_table: String.t(),
          references_field: atom(),
          association_name: atom() | nil
        }

  defstruct [:field, :references_table, :references_field, :association_name]

  @doc """
  Creates a new ForeignKey struct.

  ## Parameters

  - `field` - The foreign key field name in the current table
  - `references_table` - The name of the referenced table
  - `references_field` - The field name in the referenced table
  - `association_name` - Optional association name from Ecto schema

  ## Examples

      iex> Ecto.Relation.Schema.ForeignKey.new(:user_id, "users", :id, :user)
      %Ecto.Relation.Schema.ForeignKey{
        field: :user_id,
        references_table: "users",
        references_field: :id,
        association_name: :user
      }
  """
  @spec new(atom(), String.t(), atom(), atom() | nil) :: t()
  def new(field, references_table, references_field, association_name \\ nil) do
    %__MODULE__{
      field: field,
      references_table: references_table,
      references_field: references_field,
      association_name: association_name
    }
  end

  defimpl Inspect do
    def inspect(%Ecto.Relation.Schema.ForeignKey{} = fk, _opts) do
      "#ForeignKey<#{fk.field} -> #{fk.references_table}.#{fk.references_field}>"
    end
  end

  @doc """
  Extracts foreign key information from an Ecto schema module.

  This function analyzes the schema's associations to identify foreign keys.
  Only `belongs_to` associations create foreign keys in the current table.

  ## Parameters

  - `schema_module` - An Ecto schema module

  ## Returns

  A list of ForeignKey structs representing all foreign keys in the schema.

  ## Examples

      iex> Ecto.Relation.Schema.ForeignKey.from_ecto_schema(MyApp.Post)
      [
        %Ecto.Relation.Schema.ForeignKey{
          field: :user_id,
          references_table: "users",
          references_field: :id,
          association_name: :user
        }
      ]
  """
  @spec from_ecto_schema(module()) :: [t()]
  def from_ecto_schema(schema_module) when is_atom(schema_module) do
    associations = schema_module.__schema__(:associations)

    for assoc_name <- associations do
      assoc = schema_module.__schema__(:association, assoc_name)

      case assoc do
        %Ecto.Association.BelongsTo{
          owner_key: fk_field,
          related: related_schema,
          related_key: ref_field
        } ->
          # Get the table name from the related schema
          table_name = get_table_name(related_schema)

          new(fk_field, table_name, ref_field, assoc_name)

        _ ->
          # has_one, has_many, many_to_many don't create FKs in this table
          nil
      end
    end
    |> Enum.reject(&is_nil/1)
  end

  # Private helper to get table name from related schema
  defp get_table_name(related_schema) do
    try do
      related_schema.__schema__(:source)
    rescue
      UndefinedFunctionError ->
        # Try to resolve the module name within Test.Ecto.TestSchemas namespace
        # This handles cases where associations reference unqualified module names
        case try_resolve_test_schema(related_schema) do
          {:ok, resolved_module} -> resolved_module.__schema__(:source)
          :error -> "unknown_table"
        end
    end
  end

  # Helper to resolve test schema modules
  defp try_resolve_test_schema(module_name) do
    full_module_name = Module.concat([Test.Ecto.TestSchemas, module_name])

    if Code.ensure_loaded?(full_module_name) and
         function_exported?(full_module_name, :__schema__, 1) do
      {:ok, full_module_name}
    else
      :error
    end
  end
end

defimpl Ecto.Relation.Cache.Serializable, for: Ecto.Relation.Schema.ForeignKey do
  @moduledoc """
  Implementation of Cache.Serializable protocol for Ecto.Relation.Schema.ForeignKey.

  This implementation handles the serialization and deserialization of foreign key structs.
  """

  alias Ecto.Relation.Schema.ForeignKey

  @doc """
  Dumps a ForeignKey struct to a JSON-compatible map.

  Converts all atom fields to strings for JSON compatibility.
  """
  def dump(%ForeignKey{} = fk) do
    %{
      "__type__" => "ForeignKey",
      "field" => Atom.to_string(fk.field),
      "references_table" => fk.references_table,
      "references_field" => Atom.to_string(fk.references_field),
      "association_name" => serialize_association_name(fk.association_name)
    }
  rescue
    error ->
      reraise ArgumentError,
              "Failed to dump ForeignKey '#{fk.field}': #{Exception.message(error)}",
              __STACKTRACE__
  end

  @doc """
  Loads a ForeignKey struct from a JSON-compatible map.

  Validates the input data and reconstructs the foreign key.
  """
  def load(%{"__type__" => "ForeignKey"} = data) do
    unless is_binary(data["field"]) do
      raise ArgumentError, "ForeignKey data missing or invalid 'field' field"
    end

    unless is_binary(data["references_table"]) do
      raise ArgumentError, "ForeignKey data missing or invalid 'references_table' field"
    end

    unless is_binary(data["references_field"]) do
      raise ArgumentError, "ForeignKey data missing or invalid 'references_field' field"
    end

    %ForeignKey{
      field: String.to_atom(data["field"]),
      references_table: data["references_table"],
      references_field: String.to_atom(data["references_field"]),
      association_name: deserialize_association_name(data["association_name"])
    }
  rescue
    error in ArgumentError ->
      reraise error, __STACKTRACE__

    error ->
      reraise ArgumentError,
              "Failed to load ForeignKey from data: #{Exception.message(error)}",
              __STACKTRACE__
  end

  def load(data) do
    raise ArgumentError,
          "Invalid ForeignKey data: expected map with '__type__' => 'ForeignKey', got: #{inspect(data)}"
  end

  # Private helper functions

  defp serialize_association_name(nil), do: nil
  defp serialize_association_name(name) when is_atom(name), do: Atom.to_string(name)

  defp deserialize_association_name(nil), do: nil
  defp deserialize_association_name(name) when is_binary(name), do: String.to_atom(name)
end
