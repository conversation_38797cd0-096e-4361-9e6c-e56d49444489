defprotocol Ecto.Relation.Cache.Serializable do
  @moduledoc """
  Protocol for deserializing schema components from JSON.

  This protocol defines how schema components should be reconstructed from
  JSON-compatible data structures for caching purposes. Serialization is
  handled by `@derive Jason.Encoder` on the structs.

  The protocol ensures that:
  - Schema components can be reconstructed from JSON data
  - Complex Ecto types are properly handled during deserialization
  - Cache operations fail fast with meaningful errors

  ## Functions

  - `load/1` - Reconstructs a component from a JSON-compatible map

  ## Examples

      # Load a field from JSON (serialization handled by Jason.Encoder)
      field = Ecto.Relation.Cache.Serializable.load(json_data)

  ## Error Handling

  `load/1` should raise meaningful exceptions when it encounters invalid data
  or cannot complete the operation. This ensures cache operations fail fast
  rather than silently corrupting data.
  """

  @doc """
  Loads a schema component from a JSON-compatible map.

  This function should reconstruct the original component from JSON data.
  It should validate the input data and ensure the reconstructed component is valid.

  ## Parameters

  - `data` - The JSON-compatible map containing component data

  ## Returns

  The reconstructed schema component.

  ## Raises

  - `ArgumentError` if the data is invalid or missing required fields
  - `RuntimeError` if reconstruction fails for any other reason
  """
  @spec load(map()) :: t()
  def load(data)
end

defimpl Ecto.Relation.Cache.Serializable, for: Map do
  @moduledoc """
  Implementation of Cache.Serializable protocol for Map (JSON data).

  This implementation handles loading schema components from JSON-compatible maps
  by dispatching to the appropriate struct implementation based on the "__struct__" field.
  """

  @doc """
  Loads a schema component from a JSON-compatible map by dispatching to the appropriate implementation.
  """
  def load(%{"__struct__" => struct_name} = data) do
    # Handle Jason-encoded structs
    case struct_name do
      "Elixir.Ecto.Relation.Schema" ->
        Ecto.Relation.Cache.Serializable.Ecto.Relation.Schema.load(data)

      "Elixir.Ecto.Relation.Schema.Field" ->
        Ecto.Relation.Cache.Serializable.Ecto.Relation.Schema.Field.load(data)

      "Elixir.Ecto.Relation.Schema.PrimaryKey" ->
        Ecto.Relation.Cache.Serializable.Ecto.Relation.Schema.PrimaryKey.load(data)

      "Elixir.Ecto.Relation.Schema.ForeignKey" ->
        Ecto.Relation.Cache.Serializable.Ecto.Relation.Schema.ForeignKey.load(data)

      "Elixir.Ecto.Relation.Schema.Index" ->
        Ecto.Relation.Cache.Serializable.Ecto.Relation.Schema.Index.load(data)

      "Elixir.Ecto.Relation.Schema.Indices" ->
        Ecto.Relation.Cache.Serializable.Ecto.Relation.Schema.Indices.load(data)

      _ ->
        raise ArgumentError, "Unknown struct type: #{struct_name}"
    end
  end

  def load(data) do
    raise ArgumentError,
          "Invalid data: expected map with '__struct__' field, got: #{inspect(data)}"
  end
end
