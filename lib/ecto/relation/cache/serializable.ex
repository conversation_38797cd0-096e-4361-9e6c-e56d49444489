defprotocol Ecto.Relation.Cache.Serializable do
  @moduledoc """
  Protocol for serializing and deserializing schema components to/from JSON.

  This protocol defines how schema components should be converted to and from
  JSON-compatible data structures for caching purposes. All schema components
  must implement this protocol to be cacheable.

  The protocol ensures that:
  - All schema components can be safely serialized to JSON
  - Deserialization is the exact inverse of serialization
  - Complex Ecto types are properly handled
  - Cache operations fail fast with meaningful errors

  ## Functions

  - `dump/1` - Converts a component to a JSON-compatible map
  - `load/1` - Reconstructs a component from a JSON-compatible map

  ## Examples

      # Dump a field to JSON
      json_data = Ecto.Relation.Cache.Serializable.dump(field)

      # Load a field from JSON
      field = Ecto.Relation.Cache.Serializable.load(json_data)

  ## Error Handling

  Both `dump/1` and `load/1` should raise meaningful exceptions when they
  encounter invalid data or cannot complete the operation. This ensures
  cache operations fail fast rather than silently corrupting data.
  """

  @doc """
  Dumps a schema component to a JSON-compatible map.

  This function should convert the component to a map that can be safely
  serialized to JSON and later reconstructed using `load/1`.

  The returned map should:
  - Contain all necessary data to reconstruct the component
  - Use only JSON-compatible types (strings, numbers, booleans, lists, maps)
  - Include a "__type__" field to identify the component type
  - Handle complex Ecto types appropriately

  ## Parameters

  - `component` - The schema component to dump

  ## Returns

  A map containing all necessary data to reconstruct the component.

  ## Raises

  - `ArgumentError` if the component cannot be serialized
  - `RuntimeError` if serialization fails for any other reason
  """
  @spec dump(t()) :: map()
  def dump(component)

  @doc """
  Loads a schema component from a JSON-compatible map.

  This function should reconstruct the original component from the data
  produced by `dump/1`. It should validate the input data and ensure
  the reconstructed component is valid.

  ## Parameters

  - `data` - The JSON-compatible map containing component data

  ## Returns

  The reconstructed schema component.

  ## Raises

  - `ArgumentError` if the data is invalid or missing required fields
  - `RuntimeError` if reconstruction fails for any other reason
  """
  @spec load(map()) :: t()
  def load(data)
end

defimpl Ecto.Relation.Cache.Serializable, for: Map do
  @moduledoc """
  Implementation of Cache.Serializable protocol for Map (JSON data).

  This implementation handles loading schema components from JSON-compatible maps
  by dispatching to the appropriate struct implementation based on the "__type__" field.
  """

  @doc """
  Maps are only used for loading (from JSON), not dumping.
  """
  def dump(_map) do
    raise ArgumentError, "Cannot dump a Map directly. Use the specific struct type instead."
  end

  @doc """
  Loads a schema component from a JSON-compatible map by dispatching to the appropriate implementation.
  """
  def load(%{"__struct__" => struct_name} = data) do
    # Handle Jason-encoded structs
    case struct_name do
      "Elixir.Ecto.Relation.Schema" ->
        Ecto.Relation.Cache.Serializable.Ecto.Relation.Schema.load(data)

      "Elixir.Ecto.Relation.Schema.Field" ->
        Ecto.Relation.Cache.Serializable.Ecto.Relation.Schema.Field.load(data)

      "Elixir.Ecto.Relation.Schema.PrimaryKey" ->
        Ecto.Relation.Cache.Serializable.Ecto.Relation.Schema.PrimaryKey.load(data)

      "Elixir.Ecto.Relation.Schema.ForeignKey" ->
        Ecto.Relation.Cache.Serializable.Ecto.Relation.Schema.ForeignKey.load(data)

      "Elixir.Ecto.Relation.Schema.Index" ->
        Ecto.Relation.Cache.Serializable.Ecto.Relation.Schema.Index.load(data)

      "Elixir.Ecto.Relation.Schema.Indices" ->
        Ecto.Relation.Cache.Serializable.Ecto.Relation.Schema.Indices.load(data)

      _ ->
        raise ArgumentError, "Unknown struct type: #{struct_name}"
    end
  end

  def load(%{"__type__" => type} = data) do
    # Handle our custom protocol format (for backward compatibility)
    case type do
      "Atom" ->
        Ecto.Relation.Cache.Serializable.Atom.load(data)

      _ ->
        raise ArgumentError, "Unknown component type: #{type}"
    end
  end

  def load(data) do
    raise ArgumentError, "Invalid data: expected map with '__type__' field, got: #{inspect(data)}"
  end
end

defimpl Ecto.Relation.Cache.Serializable, for: Atom do
  @moduledoc """
  Implementation of Cache.Serializable protocol for Atom.

  This implementation handles atoms used in test scenarios by relying on JSON serialization.
  """

  @doc """
  Dumps an atom to a JSON-compatible representation.
  """
  def dump(atom) when is_atom(atom) do
    # Use JSON-compatible representation
    %{
      "__type__" => "Atom",
      "value" => Atom.to_string(atom)
    }
  end

  @doc """
  Loads an atom from a JSON-compatible representation.
  """
  def load(%{"__type__" => "Atom", "value" => atom_string}) when is_binary(atom_string) do
    String.to_atom(atom_string)
  end

  def load(data) do
    raise ArgumentError,
          "Invalid atom data: expected map with '__type__' => 'Atom', got: #{inspect(data)}"
  end
end
